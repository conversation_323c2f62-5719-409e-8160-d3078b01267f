import { Vibration } from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import uuid from 'react-native-uuid'
import { FirebaseMessagingTypes } from '@react-native-firebase/messaging'
import notifee, { EventType } from '@notifee/react-native'

// Global type declaration for foreground service resolver
declare global {
  var resolveForegroundService: (() => void) | undefined
}

import { useCallStore, CallType } from '../../stores/callStoreSimplified'
import CallSignalingService from './CallSignalingService'
import MediaService from './MediaService'
import NotificationService from './NotificationService'
import VideoSDKService from '../videosdk/VideoSDKService'
import * as NavigationService from '../../navigation/NavigationService'
import ApiService from '../ApiService'
import CallStateCleanup from '../../utils/callStateCleanup'
import { startPersistentCall, updatePersistentCallStatus, endPersistentCall } from '../../components/videosdk/PersistentMeetingManager'
import PermissionManagerService from '../PermissionManagerService'

/**
 * CallController - Main orchestration layer for call flows
 * 
 * Handles:
 * 1. Outgoing call initiation
 * 2. Incoming call handling
 * 3. Call state management via Zustand
 * 4. Notifications via NotificationService
 * 5. Media via MediaService
 * 6. Navigation coordination
 */
class CallController {
  private static _instance: CallController
  private signaling: CallSignalingService
  private media: MediaService
  private notification: NotificationService
  private videoSDK: VideoSDKService
  
  private vibrateInterval: NodeJS.Timeout | null = null
  private lastCallId?: number; // <-- Store last callId for bulletproof end call

  static getInstance() {
    if (!CallController._instance) CallController._instance = new CallController()
    return CallController._instance
  }
  
  private constructor() {
    // Initialize services
    this.signaling = CallSignalingService.getInstance()
    this.media = MediaService.getInstance()
    this.notification = NotificationService.getInstance()
    this.videoSDK = VideoSDKService.getInstance()
    
    // Set up listeners
    this.setupStoreListeners()
    this.setupNotificationListeners()
  }
  
  /**
   * Listen to store changes and coordinate actions
   */
  private setupStoreListeners() {
    const { getState, subscribe } = useCallStore
    
    // When call status changes
    subscribe(
      state => state.status,
      (status, prevStatus) => {
        console.log(`[CallController] Status changed: ${prevStatus} -> ${status}`)
        
        switch (status) {
          case 'ringing': {
            const session = getState().session
            if (session) {
              // Start vibrating
              this.startVibrate()
              // Show incoming call notification with meeting details
              this.notification.showIncomingCall(
                session.sessionId,
                session.peerName,
                session.type,
                session.meetingId,
                session.token
              )
            }
            break
          }
          
          case 'connecting': {
            // Stop vibrating
            this.stopVibrate()
            break
          }
          
          case 'in_call': {
            const session = getState().session
            if (session) {
              // Show ongoing call notification
              this.notification.showOngoingCall(
                session.sessionId,
                session.peerName, 
                session.type
              )
            }
            break
          }
          
          case 'ended': {
            // Stop vibrating
            this.stopVibrate()
            
            // Hide notifications
            const session = getState().session
            if (session) {
              this.notification.hideNotification(session.sessionId)
            }
            
            // Clean up the session
            setTimeout(() => {
              this.cleanup()
            }, 500)
            break
          }
        }
      }
    )
    
    // When status changes, update persistent meeting manager
    subscribe(
      state => state.status,
      (status, prevStatus) => {
        switch (status) {
          case 'connecting':
          case 'in_call': {
            // Update persistent meeting status
            updatePersistentCallStatus(status)
            break
          }
          
          case 'ended': {
            // End persistent call
            endPersistentCall()
            break
          }
        }
      }
    )
  }
  
  /**
   * Listen to notification interactions
   */
  private setupNotificationListeners() {
    // Set up notifee action listeners for answer/decline/end
    notifee.onForegroundEvent(({ type, detail }) => {
      if (type === EventType.ACTION_PRESS) {
        const sessionId = detail.notification?.data?.sessionId as string

        if (!sessionId) {
          console.warn('[CallController] No sessionId in notification data')
          return
        }

        console.log('[CallController] Notification action pressed:', detail.pressAction?.id)

        switch (detail.pressAction?.id) {
          case 'answer':
            this.acceptCall()
            break

          case 'decline':
          case 'end':
            this.endCall()
            break

          default:
            console.warn('[CallController] Unknown notification action:', detail.pressAction?.id)
        }
      }
    })

    // Also handle background events
    notifee.onBackgroundEvent(async ({ type, detail }) => {
      if (type === EventType.ACTION_PRESS) {
        const sessionId = detail.notification?.data?.sessionId as string

        if (!sessionId) return

        console.log('[CallController] Background notification action:', detail.pressAction?.id)

        switch (detail.pressAction?.id) {
          case 'answer':
            await this.acceptCall()
            break

          case 'decline':
          case 'end':
            await this.endCall()
            break
        }
      }
    })
  }
  
  /**
   * Start phone vibration
   */
  private startVibrate() {
    this.stopVibrate()
    Vibration.vibrate([1000, 500, 1000, 500], true)
  }
  
  /**
   * Stop phone vibration
   */
  private stopVibrate() {
    if (this.vibrateInterval) {
      clearInterval(this.vibrateInterval)
      this.vibrateInterval = null
    }
    Vibration.cancel()
  }
  
  /**
   * Clean up call resources with comprehensive state reset
   */
  private async cleanup() {
    console.log('[CallController] Starting comprehensive cleanup');

    try {
      // Use the comprehensive cleanup utility
      const cleanupService = CallStateCleanup.getInstance();
      await cleanupService.performComprehensiveCleanup();

      console.log('[CallController] Comprehensive cleanup complete');
    } catch (error) {
      console.error('[CallController] Error during comprehensive cleanup:', error);

      // Fallback to emergency cleanup
      const cleanupService = CallStateCleanup.getInstance();
      cleanupService.emergencyCleanup();
    }
  }
  
  /**
   * Get user info from storage
   */
  private async getUserInfo() {
    const userId = await AsyncStorage.getItem('userId') || '0'
    const userName = await AsyncStorage.getItem('userName') || 'Unknown User'
    return { userId, userName }
  }
  
  /**
   * Get token for peer's FCM
   */
  private async getPeerFCMToken(peerId: string) {
    try {
      const response = await ApiService.getFCMToken(peerId)
      return response?.token
    } catch (err) {
      console.warn('[CallController] Failed to get peer FCM token', err)
      return null
    }
  }
  
  /**
   * Helper to fetch FCM token for a given user id
   */
  private async fetchFcmToken(userId: string) {
    try {
      const res = await ApiService.getFCMToken(userId)
      return res?.token || null
    } catch (error) {
      console.warn('[CallController] Failed to fetch FCM token for', userId, error)
      return null
    }
  }
  
  /**
   * Helper to send call status update via ApiService
   */
  private async sendCallStatusUpdate(type: 'CALL_ENDED' | 'CALL_MISSED' | 'CALL_ACCEPTED') {
    try {
      const { userId, userName } = await this.getUserInfo()
      const callerToken = await this.fetchFcmToken(userId)
      if (!callerToken) throw new Error('Caller FCM token not found')

      await ApiService.updateCallStatus({
        callerInfo: {
          token: callerToken,
          name: userName,
          platform: require('react-native').Platform.OS === 'ios' ? 'IOS' : 'ANDROID'
        },
        type,
      })
    } catch (err) {
      console.warn('[CallController] sendCallStatusUpdate error', err)
    }
  }
  
  /**
   * Start an outgoing call
   */
  async startCall(recipientId: string, recipientName: string, callType: CallType) {
    console.log(`[CallController] Starting ${callType} call to ${recipientName}`)

    try {
      // Validate permissions before starting call
      console.log('[CallController] Validating call permissions...')
      const permissionManager = PermissionManagerService.getInstance()
      const permissionResult = await permissionManager.requestCallPermissions(callType === 'video')

      if (!permissionResult.microphone) {
        console.error('[CallController] Microphone permission not granted')
        throw new Error('Microphone permission is required to make calls')
      }

      if (callType === 'video' && !permissionResult.camera) {
        console.error('[CallController] Camera permission not granted for video call')
        throw new Error('Camera permission is required to make video calls')
      }

      console.log('[CallController] Call permissions validated successfully:', permissionResult)

      // Ensure comprehensive cleanup before starting new call
      await this.cleanup()

      // Ensure VideoSDK is initialized
      await this.videoSDK.initialize()

      // Clear any existing meeting state to prevent conflicts
      await this.videoSDK.clearExistingMeetingState()

      // Get local user info
      const { userId, userName } = await this.getUserInfo()

      // Use consolidated API that combines token generation, meeting creation, and call initiation
      console.log('🚀 [CallController] Making consolidated call API request:', {
        callerId: parseInt(userId),
        receiverId: parseInt(recipientId),
        callType,
        platform: require('react-native').Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
        timestamp: new Date().toISOString()
      });

      const consolidatedResponse = await ApiService.initiateConsolidatedCall({
        callerId: parseInt(userId),
        receiverId: parseInt(recipientId),
        callType,
        platform: require('react-native').Platform.OS === 'ios' ? 'IOS' : 'ANDROID'
      });

      console.log('✅ [CallController] Consolidated call API response received:', {
        success: consolidatedResponse.success,
        callId: consolidatedResponse.data.callId,
        meetingId: consolidatedResponse.data.meetingId,
        sessionId: consolidatedResponse.data.sessionId,
        timestamp: new Date().toISOString()
      });

      if (!consolidatedResponse.success) {
        throw new Error(consolidatedResponse.message || 'Failed to initiate call');
      }

      // Extract data from consolidated response
      const {
        callId,
        meetingId,
        token,
        sessionId: backendSessionId,
        channelName,
        maxDuration
      } = consolidatedResponse.data;

      // Store callId for later use
      this.lastCallId = callId;

      // Set the backend sessionId as the active meeting session
      if (!this.videoSDK.setActiveMeetingSession(backendSessionId)) {
        throw new Error('Another meeting session is already active')
      }

      console.log('✅ [CallController] Consolidated API completed successfully - token generated, meeting created, FCM sent, payment tracked');

      // Update store with outgoing call (use sessionId from consolidated API response)
      const store = useCallStore.getState()
      store.actions.setSession({
        sessionId: backendSessionId, // Use sessionId from backend for consistency
        meetingId,
        token,
        peerId: recipientId,
        peerName: recipientName,
        direction: 'outgoing',
        type: callType,
        startedAt: Date.now(),
        callId // Store the callId for payment processing when ending the call
      })
      store.actions.setStatus('outgoing')
      
      // Initialize media
      await this.media.initialize()
      
      // Show outgoing call notification
      this.notification.showOngoingCall(backendSessionId, recipientName, callType)

      // For outgoing calls, immediately transition to connecting so the meeting screen can render
      store.actions.setStatus('connecting')

      // Start persistent call instead of navigating
      startPersistentCall({
        sessionId: backendSessionId,
        meetingId,
        token,
        peerName: recipientName,
        callType,
        direction: 'outgoing'
      })
      
      return true
    } catch (error) {
      console.error('[CallController] startCall error', error)
      
      // Reset call state
      const store = useCallStore.getState()
      store.actions.reset()
      
      return false
    }
  }
  
  /**
   * Accept incoming call
   */
  async acceptCall() {
    const store = useCallStore.getState()
    const session = store.session

    if (!session || store.status !== 'ringing') {
      console.warn('[CallController] Cannot accept call - no session or not ringing')
      return false
    }

    try {
      console.log('[CallController] Accepting call:', session.sessionId)

      // Validate permissions before accepting call
      console.log('[CallController] Validating call permissions for incoming call...')
      const permissionManager = PermissionManagerService.getInstance()
      const permissionResult = await permissionManager.requestCallPermissions(session.type === 'video')

      if (!permissionResult.microphone) {
        console.error('[CallController] Microphone permission not granted for accepting call')
        // Decline the call if permissions are not granted
        await this.declineCall()
        return false
      }

      if (session.type === 'video' && !permissionResult.camera) {
        console.error('[CallController] Camera permission not granted for accepting video call')
        // Decline the call if camera permission is not granted for video call
        await this.declineCall()
        return false
      }

      console.log('[CallController] Call permissions validated for accepting call:', permissionResult)

      // Stop vibrating
      this.stopVibrate()

      // Hide incoming notification
      this.notification.hideNotification(session.sessionId)

      // Update status
      store.actions.setStatus('connecting')

      // Initialize media and join meeting
      await this.media.initialize()

      // Join the meeting if we have meeting details
      if (session.meetingId && session.token) {
        await this.media.joinMeeting(
          session.meetingId,
          session.token,
          session.peerName || 'User',
          'video' // Default to video, will be updated by the meeting screen
        )
      }

      // Send accept signal
      try {
        await this.signaling.sendAccept(session.peerId, session.sessionId)
      } catch (signalError) {
        console.error('[CallController] Failed to send accept signal:', signalError)
      }

      // Start payment tracking for accepted call (if not already started)
      if (!session.callId) {
        try {
          console.log(`[CallController] Starting payment tracking for accepted ${session.type} call`)
          const { userId } = await this.getUserInfo()

          const paymentResponse = session.type === 'video'
            ? await ApiService.initiateVideoCall({
                callerId: parseInt(session.peerId), // The original caller
                receiverId: parseInt(userId), // Current user (receiver)
                action: 'start'
              })
            : await ApiService.initiateVoiceCall({
                callerId: parseInt(session.peerId), // The original caller
                receiverId: parseInt(userId), // Current user (receiver)
                action: 'start'
              })

          // Fix: Support both callId and call_id from backend
          const callId = paymentResponse.callId || paymentResponse.call_id;
          if (paymentResponse.status && callId) {
            this.lastCallId = callId; // <-- Store callId for later use
            // Update session with callId
            store.actions.setSession({
              ...session,
              callId
            })
            console.log(`[CallController] Payment tracking started for accepted call, callId: ${callId}`)
          } else {
            console.warn('[CallController] Payment API call succeeded but no callId returned:', paymentResponse)
          }
        } catch (paymentError) {
          console.error('[CallController] Failed to start payment tracking for accepted call:', paymentError)
          // Continue with call even if payment tracking fails
        }
      }

      // Notify server of accepted call
      try {
        await this.sendCallStatusUpdate('CALL_ACCEPTED')
      } catch (statusError) {
        console.error('[CallController] Failed to send call status update:', statusError)
      }

      // Update status to in_call
      store.actions.setStatus('in_call')

      return true
    } catch (error) {
      console.error('[CallController] acceptCall error', error)
      return false
    }
  }
  
  /**
   * Decline incoming call
   */
  async declineCall() {
    const store = useCallStore.getState()
    const session = store.session
    
    if (!session || store.status !== 'ringing') return false
    
    try {
      // Stop vibrating
      this.stopVibrate()
      
      // Hide incoming notification
      this.notification.hideNotification(session.sessionId)
      
      // Send end signal
      try {
        await this.signaling.sendEnd(session.peerId, session.sessionId)
      } catch (signalError) {
        console.error('[CallController] Failed to send decline signal:', signalError)
      }

      // Notify server of missed/declined call
      try {
        await this.sendCallStatusUpdate('CALL_MISSED')
      } catch (statusError) {
        console.error('[CallController] Failed to send call status update:', statusError)
      }

      // CallManagerService removed - billing handled by CallBillingService
      console.log('ℹ️ [CallController] Missed call cleanup completed')
      
      // Update status
      store.actions.setStatus('ended')
      
      return true
    } catch (error) {
      console.error('[CallController] declineCall error', error)
      return false
    }
  }
  
  /**
   * End active call
   */
  async endCall() {
    const store = useCallStore.getState()
    let session = store.session

    if (!session) {
      console.warn('[CallController] No session found in store on endCall');
      return false;
    }

    // Debug: Log session object
    console.log('[CallController] Session object on endCall:', session);

    // Use lastCallId if available, otherwise session.callId
    const callIdToUse = this.lastCallId || session.callId;
    if (!callIdToUse) {
      console.warn('[CallController] No callId available for end call', session);
    } else {
      // Call the end API (voice or video)
      const { userId } = await this.getUserInfo();
      
      try {
        // Always use the original caller and receiver from when call started
        // For outgoing calls: current user is caller, peer is receiver
        // For incoming calls: peer is caller, current user is receiver
        const isOutgoingCall = session.direction === 'outgoing';
        const originalCallerId = isOutgoingCall ? parseInt(userId) : parseInt(session.peerId);
        const originalReceiverId = isOutgoingCall ? parseInt(session.peerId) : parseInt(userId);
        
        const payload = {
          callerId: originalCallerId,
          receiverId: originalReceiverId,
          action: 'end' as const,
          callId: callIdToUse
        };
        
        console.log('[CallController] 🚀 Calling end API with payload:', payload);
        console.log('[CallController] 📞 Call type:', session.type);
        console.log('[CallController] 📤 Call direction:', session.direction);
        console.log('[CallController] 👤 Original caller ID:', originalCallerId);
        console.log('[CallController] 👥 Original receiver ID:', originalReceiverId);
        console.log('[CallController] 🆔 Call ID:', callIdToUse);
        
        if (session.type === 'video') {
          console.log('[CallController] 📹 Making video call end API call...');
          await ApiService.initiateVideoCall(payload);
          console.log('[CallController] ✅ Video call end API called successfully');
        } else {
          console.log('[CallController] 📞 Making voice call end API call...');
          await ApiService.initiateVoiceCall(payload);
          console.log('[CallController] ✅ Voice call end API called successfully');
        }
        
        console.log('[CallController] 🎉 End call API completed successfully');
      } catch (err) {
        console.error('[CallController] ❌ End call API error:', err);
        console.error('[CallController] 📋 Error details:', {
          error: err,
          payload: {
            callerId: session.direction === 'outgoing' ? parseInt(userId) : parseInt(session.peerId),
            receiverId: session.direction === 'outgoing' ? parseInt(session.peerId) : parseInt(userId),
            action: 'end',
            callId: callIdToUse
          },
          sessionType: session.type,
          sessionDirection: session.direction
        });
      }
    }

    // Proceed with UI cleanup immediately
    try {
      // Stop vibrating
      this.stopVibrate()

      // Update status
      store.actions.setStatus('ended')
      // Clear active meeting session in VideoSDK service
      if (session && session.sessionId) {
        this.videoSDK.clearActiveMeetingSession(session.sessionId)
      }
      // Send end signal
      try {
        if (session && session.peerId && session.sessionId) {
          await this.signaling.sendEnd(session.peerId, session.sessionId)
        }
      } catch (signalError) {
        console.error('[CallController] Failed to send end signal:', signalError)
      }
      // Leave meeting
      try {
        await this.media.leaveMeeting()
      } catch (mediaError) {
        console.error('[CallController] Failed to leave meeting:', mediaError)
      }
      // Hide notifications and stop foreground service
      try {
        if (session && session.sessionId) {
          this.notification.hideNotification(session.sessionId)
        }
        if (global.resolveForegroundService) {
          global.resolveForegroundService()
        }
        await notifee.stopForegroundService()
      } catch (notificationError) {
        console.error('[CallController] Failed to cleanup notifications:', notificationError)
      }
      console.log('ℹ️ [CallController] Call cleanup completed')
    } catch (cleanupError) {
      console.error('[CallController] Error during call cleanup:', cleanupError)
    }
    return true;
  }
  
  /**
   * Handle incoming FCM message for call
   */
  handleFCMMessage(message: FirebaseMessagingTypes.RemoteMessage) {
    console.log('[CallController] Handling FCM message', message.data)

    try {
      const data = message.data
      if (!data || !data.type) {
        console.log('[CallController] No call data in FCM message, ignoring')
        return
      }

      const messageType = data.type
      console.log('[CallController] Processing FCM message type:', messageType)

      switch (messageType) {
        case 'CALL_INITIATE':
          this.handleIncomingCallFCM(data)
          break
        case 'CALL_ACCEPT':
          this.handleCallAcceptFCM(data)
          break
        case 'CALL_END':
          this.handleCallEndFCM(data)
          break
        default:
          console.log('[CallController] Unknown FCM message type:', messageType)
      }
    } catch (error) {
      console.error('[CallController] Error handling FCM message:', error)
      // Don't throw - just log the error to prevent app crashes
    }
  }

  /**
   * Handle incoming call FCM message
   */
  private handleIncomingCallFCM(data: any) {
    try {
      console.log('[CallController] Handling incoming call FCM:', data)

      const sessionId = data.sessionId
      const callerName = data.callerName || 'Unknown Caller'
      const callType = data.callType || 'voice'
      const meetingId = data.meetingId
      const token = data.token
      const callerId = data.callerId

      if (!sessionId || !meetingId || !token) {
        console.error('[CallController] Missing required call data in FCM message')
        return
      }

      // Update call store with incoming call
      const store = useCallStore.getState()
      store.actions.setSession({
        sessionId,
        meetingId,
        token,
        peerId: callerId,
        peerName: callerName,
        direction: 'incoming',
        type: callType as CallType,
        startedAt: Date.now()
      })
      store.actions.setStatus('ringing')

      // Show incoming call notification
      this.notification.showIncomingCall(sessionId, callerName, callType)

      // Start vibration
      this.startVibrate()

      console.log('[CallController] Incoming call FCM processed successfully')
    } catch (error) {
      console.error('[CallController] Error handling incoming call FCM:', error)
    }
  }

  /**
   * Handle call accept FCM message
   */
  private handleCallAcceptFCM(data: any) {
    try {
      console.log('[CallController] Handling call accept FCM:', data)

      const sessionId = data.sessionId
      const store = useCallStore.getState()

      if (store.session?.sessionId === sessionId) {
        store.actions.setStatus('connecting')
        console.log('[CallController] Call accept FCM processed successfully')
      }
    } catch (error) {
      console.error('[CallController] Error handling call accept FCM:', error)
    }
  }

  /**
   * Handle call end FCM message
   */
  private handleCallEndFCM(data: any) {
    try {
      console.log('[CallController] Handling call end FCM:', data)

      const sessionId = data.sessionId
      const store = useCallStore.getState()

      if (store.session?.sessionId === sessionId) {
        // Stop vibration
        this.stopVibrate()

        // Hide notifications
        this.notification.hideNotification(sessionId)

        // Update status to ended
        store.actions.setStatus('ended')

        console.log('[CallController] Call end FCM processed successfully')
      }
    } catch (error) {
      console.error('[CallController] Error handling call end FCM:', error)
    }
  }

  /**
   * Get media service instance
   */
  getMediaService() {
    return this.media
  }
}

export default CallController 