[useUsers] getNextPageParam: {totalRecords: 59548, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
TipCallScreenSimple.tsx:724 [TipCallScreen] Requesting call permissions for voice call
PermissionManagerService.ts:232 [PermissionManager] Requesting call permissions... {includeCamera: false}
PermissionManagerService.ts:268 [PermissionManager] Call permissions result: {microphone: true, camera: false}
TipCallScreenSimple.tsx:746 [TipCallScreen] Call permissions granted: {camera: false, microphone: true}
CallBillingService.ts:77 [CallBillingService] Calculating billing for: {userId: '58422', callType: 'voice', currentBalance: 1589.16, isPremium: false}
CallBillingService.ts:101 [CallBillingService] Calculated billing: {ratePerMinute: 7, maxMinutes: 227, maxDurationSeconds: 13620, warningThresholds: Array(5)}
TipCallScreenSimple.tsx:829 🚀 [TipCallScreenSimple] Starting call via CallController: {recipientId: '58463', recipientName: 'Rishika', callType: 'voice', timestamp: '2025-07-29T08:33:55.801Z'}
CallController.ts:316 [CallController] Starting voice call to Rishika
CallController.ts:320 [CallController] Validating call permissions...
PermissionManagerService.ts:232 [PermissionManager] Requesting call permissions... {includeCamera: false}
TipCallScreenSimple.tsx:868 Warning: useInsertionEffect must not schedule updates.
    at Animated(View) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111432:47)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at AppContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102315:25)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at VirtualizedListContextResetter (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:115926:24)
    at RCTModalHostView (<anonymous>)
    at Modal (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129248:36)
    at CallConfirmationAlert (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:416337:23)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at TipCallScreenSimple (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:413126:97)
    at StaticContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163285:17)
    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158902:24)
    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163125:22)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at Animated(View) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111432:47)
    at Background (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=tru
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:260
anonymous @ LogBox.js:80
printWarning @ ReactFabric-dev.js:160
error$jscomp$0 @ ReactFabric-dev.js:151
scheduleUpdateOnFiber @ ReactFabric-dev.js:11403
dispatchSetStateInternal @ ReactFabric-dev.js:5873
dispatchSetState @ ReactFabric-dev.js:5826
anonymous @ TipCallScreenSimple.tsx:868
anonymous @ CallConfirmationAlert.tsx:118
cb @ AnimatedImplementation.js:388
anonymous @ AnimatedValue.js:325
__notifyAnimationEnd @ Animation.js:179
stop @ TimingAnimation.js:172
stopAnimation @ AnimatedValue.js:258
stop @ AnimatedImplementation.js:242
anonymous @ AnimatedImplementation.js:407
stop @ AnimatedImplementation.js:406
cb @ AnimatedImplementation.js:393
anonymous @ AnimatedValue.js:325
__notifyAnimationEnd @ Animation.js:179
stop @ TimingAnimation.js:172
stopAnimation @ AnimatedValue.js:258
__detach @ AnimatedValue.js:116
__removeChild @ AnimatedWithChildren.js:64
__detach @ AnimatedStyle.js:211
__removeChild @ AnimatedWithChildren.js:64
__detach @ AnimatedProps.js:175
anonymous @ createAnimatedPropsHook.js:290
reactStackBottomFrame @ ReactFabric-dev.js:14861
runWithFiberInDEV @ ReactFabric-dev.js:571
commitHookEffectListUnmount @ ReactFabric-dev.js:9665
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10307
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10427
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10493
commitMutationEffects @ ReactFabric-dev.js:10419
commitRootImpl @ ReactFabric-dev.js:12443
commitRootWhenReady @ ReactFabric-dev.js:11705
performWorkOnRoot @ ReactFabric-dev.js:11652
performWorkOnRootViaSchedulerTask @ ReactFabric-dev.js:2807
Show 507 more frames
Show less
TipCallScreenSimple.tsx:869 Warning: useInsertionEffect must not schedule updates.
    at Animated(View) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111432:47)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at AppContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102315:25)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at VirtualizedListContextResetter (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:115926:24)
    at RCTModalHostView (<anonymous>)
    at Modal (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129248:36)
    at CallConfirmationAlert (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:416337:23)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at TipCallScreenSimple (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:413126:97)
    at StaticContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163285:17)
    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158902:24)
    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163125:22)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102424:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24383:79)
    at Animated(View) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111432:47)
    at Background (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=tru
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:260
anonymous @ LogBox.js:80
printWarning @ ReactFabric-dev.js:160
error$jscomp$0 @ ReactFabric-dev.js:151
scheduleUpdateOnFiber @ ReactFabric-dev.js:11403
dispatchSetStateInternal @ ReactFabric-dev.js:5873
dispatchSetState @ ReactFabric-dev.js:5826
anonymous @ TipCallScreenSimple.tsx:869
anonymous @ CallConfirmationAlert.tsx:118
cb @ AnimatedImplementation.js:388
anonymous @ AnimatedValue.js:325
__notifyAnimationEnd @ Animation.js:179
stop @ TimingAnimation.js:172
stopAnimation @ AnimatedValue.js:258
stop @ AnimatedImplementation.js:242
anonymous @ AnimatedImplementation.js:407
stop @ AnimatedImplementation.js:406
cb @ AnimatedImplementation.js:393
anonymous @ AnimatedValue.js:325
__notifyAnimationEnd @ Animation.js:179
stop @ TimingAnimation.js:172
stopAnimation @ AnimatedValue.js:258
__detach @ AnimatedValue.js:116
__removeChild @ AnimatedWithChildren.js:64
__detach @ AnimatedStyle.js:211
__removeChild @ AnimatedWithChildren.js:64
__detach @ AnimatedProps.js:175
anonymous @ createAnimatedPropsHook.js:290
reactStackBottomFrame @ ReactFabric-dev.js:14861
runWithFiberInDEV @ ReactFabric-dev.js:571
commitHookEffectListUnmount @ ReactFabric-dev.js:9665
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10307
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10427
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10493
commitMutationEffects @ ReactFabric-dev.js:10419
commitRootImpl @ ReactFabric-dev.js:12443
commitRootWhenReady @ ReactFabric-dev.js:11705
performWorkOnRoot @ ReactFabric-dev.js:11652
performWorkOnRootViaSchedulerTask @ ReactFabric-dev.js:2807
Show 507 more frames
Show less
PermissionManagerService.ts:268 [PermissionManager] Call permissions result: {microphone: true, camera: false}
CallController.ts:334 [CallController] Call permissions validated successfully: {camera: false, microphone: true}
CallController.ts:238 [CallController] Starting comprehensive cleanup
callStateCleanup.ts:76 [CallStateCleanup] Starting comprehensive cleanup
callStateCleanup.ts:112 [CallStateCleanup] Cleaning up media service
MediaService.ts:119 [MediaService] Starting comprehensive meeting cleanup
VideoSDKService.ts:266 [VideoSDK] Starting comprehensive service reset
VideoSDKService.ts:296 [VideoSDK] Forcing WebRTC connection cleanup
VideoSDKService.ts:302 [VideoSDK] Clearing component instance tracking
VideoSDKService.ts:335 [VideoSDK] Service reset complete
MediaService.ts:167 [MediaService] Comprehensive meeting cleanup completed
callStateCleanup.ts:136 [CallStateCleanup] Resetting VideoSDK service
VideoSDKService.ts:266 [VideoSDK] Starting comprehensive service reset
VideoSDKService.ts:296 [VideoSDK] Forcing WebRTC connection cleanup
VideoSDKService.ts:302 [VideoSDK] Clearing component instance tracking
VideoSDKService.ts:335 [VideoSDK] Service reset complete
2VideoSDKService.ts:332 [VideoSDK] Service reset complete with delay
callStateCleanup.ts:150 [CallStateCleanup] Cleaning up call store
callStoreSimplified.ts:58 [CallStore] Performing comprehensive reset
callStoreSimplified.ts:64 [CallStore] Reset complete
callStateCleanup.ts:162 [CallStateCleanup] Cleaning up WebRTC connections
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
callStateCleanup.ts:193 [CallStateCleanup] Clearing participant cache
callStateCleanup.ts:235 [CallStateCleanup] Forcing garbage collection
callStateCleanup.ts:97 [CallStateCleanup] Comprehensive cleanup completed successfully
CallController.ts:245 [CallController] Comprehensive cleanup complete
VideoSDKService.ts:55 [VideoSDK] Initializing...
callStateCleanup.ts:225 [CallStateCleanup] Participant cache clearing completed
VideoSDKService.ts:65 [VideoSDK] Initialization complete
VideoSDKService.ts:223 [VideoSDK] Clearing existing meeting state to prevent conflicts
VideoSDKService.ts:256 [VideoSDK] Meeting state cleared successfully
CallController.ts:349 🚀 [CallController] Making consolidated call API request: {callerId: 58422, receiverId: 58463, callType: 'voice', platform: 'ANDROID', timestamp: '2025-07-29T08:33:59.276Z'}
ApiService.ts:246 Request to protected endpoint: /api/adtipcall. Attempting to add Authorization header.
ApiService.ts:253 Authorization header added to request for: /api/adtipcall
ApiService.ts:260 🚀 API REQUEST: {method: 'POST', url: '/api/adtipcall', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/adtipcall', headers: {…}, params: undefined, data: {…}, timeout: 60000, timestamp: '2025-07-29T08:33:59.290Z'}
ProductionLogger.ts:96 [NET:ApiService] POST http://*************:7082/api/adtipcall (200) {statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-29T08:34:00.665Z'}
CallController.ts:364 ✅ [CallController] Consolidated call API response received: {success: true, callId: 50, meetingId: 'd2wg-7syu-hxeq', sessionId: 'c254f7e3-2a9e-49f8-bd77-a3286cd7d648', timestamp: '2025-07-29T08:34:00.672Z'}
VideoSDKService.ts:357 [VideoSDK] Setting active meeting session: c254f7e3-2a9e-49f8-bd77-a3286cd7d648
CallController.ts:394 ✅ [CallController] Consolidated API completed successfully - token generated, meeting created, FCM sent, payment tracked
CallController.ts:72 [CallController] Status changed: idle -> outgoing
VideoSDKService.ts:48 [VideoSDK] Already initialized
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
CallController.ts:72 [CallController] Status changed: outgoing -> connecting
PersistentMeetingManager.tsx:482 [PersistentMeetingManager] Starting new call: c254f7e3-2a9e-49f8-bd77-a3286cd7d648
PersistentMeetingManager.tsx:507 [PersistentMeetingManager] Persistent call started successfully
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
TipCallScreenSimple.tsx:842 📞 [TipCallScreenSimple] Call initiation result: {success: true, recipientId: '58463', callType: 'voice', timestamp: '2025-07-29T08:34:00.808Z'}
TipCallScreenSimple.tsx:850 ✅ [TipCallScreenSimple] Call started successfully - API calls should be logged by CallController
console.js:654 Bluetooth Connect Permission Granted
PersistentMeetingManager.tsx:253 [PersistentMeetingContent] Setting meeting reference for session: c254f7e3-2a9e-49f8-bd77-a3286cd7d648
MediaService.ts:232 [MediaService] Meeting reference set: true (meetingId: undefined)
PersistentMeetingManager.tsx:267 [PersistentMeetingContent] Join attempt 1 for session: c254f7e3-2a9e-49f8-bd77-a3286cd7d648
PersistentMeetingManager.tsx:270 [PersistentMeetingContent] Joining meeting with ID: d2wg-7syu-hxeq
PersistentMeetingManager.tsx:273 [PersistentMeetingContent] Successfully joined meeting
CallController.ts:72 [CallController] Status changed: connecting -> in_call
2ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:425763 Error while trying to reconnect websocket error
PersistentMeetingManager.tsx:108 [PersistentControls] Stopping media streams before ending call
CallController.ts:650 [CallController] Session object on endCall: {sessionId: 'c254f7e3-2a9e-49f8-bd77-a3286cd7d648', meetingId: 'd2wg-7syu-hxeq', token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlrZXkiOiI2MjU3MmY1Yy01NmFkLTRiMjktYmFlNi01MTg2N2ZmYWI2MDkiLCJwZXJtaXNzaW9ucyI6WyJhbGxvd19qb2luIiwiYWxsb3dfbW9kIl0sImlhdCI6MTc1Mzc3ODAzOCwiZXhwIjoxNzUzNzc5ODM4fQ.87q9vlw9Jm0EKWRpUPtdStZt6D7wO6oP9QTd-l5-u9U', peerId: '58463', peerName: 'Rishika', direction: 'outgoing', type: 'voice', startedAt: 1753778040678, callId: 50}
CallController.ts:675 [CallController] 🚀 Calling end API with payload: {callerId: 58422, receiverId: 58463, action: 'end', callId: 50}
CallController.ts:676 [CallController] 📞 Call type: voice
CallController.ts:677 [CallController] 📤 Call direction: outgoing
CallController.ts:678 [CallController] 👤 Original caller ID: 58422
CallController.ts:679 [CallController] 👥 Original receiver ID: 58463
CallController.ts:680 [CallController] 🆔 Call ID: 50
CallController.ts:687 [CallController] 📞 Making voice call end API call...
ApiService.ts:246 Request to protected endpoint: /api/voice-call. Attempting to add Authorization header.
ApiService.ts:253 Authorization header added to request for: /api/voice-call
ApiService.ts:260 🚀 API REQUEST: {method: 'POST', url: '/api/voice-call', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/voice-call', headers: {…}, params: undefined, data: {…}, timeout: 60000, timestamp: '2025-07-29T08:34:26.047Z'}
ApiService.ts:303 ❌ API ERROR RESPONSE: {method: 'POST', url: '/api/voice-call', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/voice-call', status: 400, statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-29T08:34:26.216Z'}
ApiService.ts:633 API Error Details (handleError): {isAxiosError: true, status: 400, statusText: undefined, data: {…}, message: 'Request failed with status code 400', config: {…}}
CallController.ts:694 [CallController] ❌ End call API error: Error: VideoSDK call record not found.
    at handleError (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143513:29)
    at ?anon_0_ (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:143374:35)
    at throw (native)
    at asyncGeneratorStep (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1418:19)
    at _throw (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:1435:29)
    at tryCallOne (address at InternalBytecode.js:1:1180)
    at anonymous (address at InternalBytecode.js:1:1874)
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ CallController.ts:694
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
CallController.ts:695 [CallController] 📋 Error details: {error: Error: VideoSDK call record not found.
    at handleError (http://localhost:8081/index.bundle//&pla…, payload: {…}, sessionType: 'voice', sessionDirection: 'outgoing'}
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
?anon_0_ @ CallController.ts:695
asyncGeneratorStep @ asyncToGenerator.js:3
_throw @ asyncToGenerator.js:20
Show 8 more frames
Show less
CallController.ts:72 [CallController] Status changed: in_call -> ended
NotificationService.ts:151 [NotificationService] Hiding notification: c254f7e3-2a9e-49f8-bd77-a3286cd7d648
PersistentMeetingManager.tsx:418 [PersistentMeetingManager] Resetting call state
PersistentMeetingManager.tsx:210 [PersistentMeetingContent] Resetting for new call: c254f7e3-2a9e-49f8-bd77-a3286cd7d648
PersistentMeetingManager.tsx:214 [PersistentMeetingContent] Stopping all media streams before reset
MediaService.ts:232 [MediaService] Meeting reference set: false 