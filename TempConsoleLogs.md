API REQUEST: {method: 'POST', url: '/api/adtipcall', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/adtipcall', headers: {…}, params: undefined, data: {…}, timeout: 60000, timestamp: '2025-07-29T08:06:49.656Z'}baseURL: "http://*************:7082"data: {callerId: 58422, receiverId: 4586, callType: 'voice', platform: 'ANDROID'}fullURL: "http://*************:7082/api/adtipcall"headers: {Accept: 'application/json', Content-Type: 'application/json', Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.RJobg0OylKEIpszdpaxjaGm2GevzrCyziXtk4vC162w'}method: "POST"params: undefinedtimeout: 60000timestamp: "2025-07-29T08:06:49.656Z"url: "/api/adtipcall"[[Prototype]]: Object
ApiService.ts:303 ❌ API ERROR RESPONSE: {method: 'POST', url: '/api/adtipcall', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/adtipcall', status: 409, statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-29T08:06:49.819Z'}baseURL: "http://*************:7082"data: error: "User is already in an active call"success: false[[Prototype]]: ObjectfullURL: "http://*************:7082/api/adtipcall"headers: {access-control-allow-credentials: 'true', access-control-allow-headers: 'Origin, X-Requested-With, Content-Type, Accept', access-control-allow-methods: 'GET, POST, PUT, DELETE', access-control-allow-origin: '*', connection: 'keep-alive', content-length: '61', content-security-policy: "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", content-type: 'application/json; charset=utf-8', cross-origin-embedder-policy: 'require-corp', cross-origin-opener-policy: 'same-origin', …}method: "POST"status: 409statusText: undefinedtimestamp: "2025-07-29T08:06:49.819Z"url: "/api/adtipcall"[[Prototype]]: Object
ApiService.ts:633 API Error Details (handleError): {isAxiosError: true, status: 409, statusText: undefined, data: {…}, message: 'Request failed with status code 409', config: {…}}
CallController.ts:424 [CallController] startCall error Error: Request failed with status code 409
    at handleError (